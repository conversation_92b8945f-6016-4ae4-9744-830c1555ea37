#!/usr/bin/env python3
"""
YOLO11 目标检测推理脚本
基于Ultralytics官方命令格式，支持图片和视频识别
支持TensorRT加速和多GPU并行推理
"""

from ultralytics import YOLO
from pathlib import Path
import argparse
import sys
import threading
import time
import cv2
from concurrent.futures import ThreadPoolExecutor, as_completed
import torch
import queue
from threading import Thread
import os
import numpy as np
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

def suppress_opencv_warnings():
    """抑制OpenCV的H.264解码警告"""
    try:
        # 设置OpenCV日志级别为ERROR，抑制WARNING信息
        cv2.setLogLevel(cv2.LOG_LEVEL_ERROR)
    except AttributeError:
        # 如果OpenCV版本不支持LOG_LEVEL_ERROR，尝试其他方法
        try:
            cv2.setLogLevel(3)  # 3 = ERROR level
        except:
            pass  # 忽略错误，继续执行

def _detect_source_type(source):
    """
    检测输入源类型

    Args:
        source: 输入源路径

    Returns:
        str: 'image', 'video', 'directory', 'camera', 'url'
    """
    # 检查是否为数字（摄像头）
    if str(source).isdigit():
        return 'camera'

    # 检查是否为URL
    if str(source).startswith(('http://', 'https://', 'rtmp://', 'rtsp://')):
        return 'url'

    # 检查是否为文件路径
    source_path = Path(source)
    if source_path.exists():
        if source_path.is_dir():
            return 'directory'
        elif source_path.is_file():
            # 根据文件扩展名判断
            ext = source_path.suffix.lower()
            video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.m4v', '.3gp', '.webm'}
            if ext in video_extensions:
                return 'video'
            else:
                return 'image'

    # 默认返回image
    return 'image'

def get_output_resolution(original_width, original_height, target_size):
    """
    根据目标尺寸计算输出分辨率

    Args:
        original_width: 原始宽度
        original_height: 原始高度
        target_size: 目标尺寸 ('original', '1080p', '720p', '480p')

    Returns:
        tuple: (width, height)
    """
    if target_size == 'original':
        return original_width, original_height
    elif target_size == '1080p':
        # 保持宽高比，高度限制为1080
        aspect_ratio = original_width / original_height
        if aspect_ratio > 16/9:  # 宽屏
            return 1920, int(1920 / aspect_ratio)
        else:
            return int(1080 * aspect_ratio), 1080
    elif target_size == '720p':
        # 保持宽高比，高度限制为720
        aspect_ratio = original_width / original_height
        if aspect_ratio > 16/9:  # 宽屏
            return 1280, int(1280 / aspect_ratio)
        else:
            return int(720 * aspect_ratio), 720
    elif target_size == '480p':
        # 保持宽高比，高度限制为480
        aspect_ratio = original_width / original_height
        if aspect_ratio > 16/9:  # 宽屏
            return 854, int(854 / aspect_ratio)
        else:
            return int(480 * aspect_ratio), 480
    else:
        return original_width, original_height

def find_model():
    """查找可用模型"""
    model_paths = [
        "runs/train/*/weights/best.pt",
        "runs/train/*/weights/last.pt",
        "yolo11n.pt",
        "yolo11s.pt",
        "yolo11m.pt",
        "yolo11l.pt",
        "yolo11x.pt"
    ]

    for pattern in model_paths:
        if '*' in pattern:
            # 使用glob查找匹配的路径
            matches = list(Path('.').glob(pattern))
            if matches:
                return str(matches[0])
        else:
            if Path(pattern).exists():
                return pattern
    return None

def export_tensorrt(model_path, **kwargs):
    """
    导出TensorRT模型

    Args:
        model_path: 原始模型路径
        **kwargs: 导出参数
    """
    print(f"🔧 导出TensorRT模型...")
    print(f"📦 原始模型: {model_path}")

    try:
        model = YOLO(model_path)

        # TensorRT导出参数
        export_args = {
            'format': 'engine',
            'imgsz': kwargs.get('imgsz', 640),
            'half': kwargs.get('half', True),  # FP16精度
            'int8': kwargs.get('int8', False),  # INT8量化
            'dynamic': kwargs.get('dynamic', False),  # 动态batch
            'workspace': kwargs.get('workspace', 16),  # 工作空间大小(GB)
            'device': kwargs.get('device', 0),
            'simplify':kwargs.get('simplify', True),
            'verbose': False
        }

        if export_args['int8']:
            export_args['data'] = kwargs.get('data', 'coco.yaml')

        print("🔧 TensorRT导出配置:")
        for key, value in export_args.items():
            print(f"  {key}: {value}")

        # 执行导出
        model.export(**export_args)

        engine_path = str(Path(model_path).with_suffix('.engine'))
        print(f"✅ TensorRT模型导出成功: {engine_path}")
        return engine_path

    except Exception as e:
        print(f"❌ TensorRT导出失败: {e}")
        return None

def frame_processor_worker(process_queue, write_queue, output_size, output_width, output_height, worker_id):
    """
    帧处理工作线程 - 专门处理CPU密集型的图像处理

    Args:
        process_queue: 待处理帧队列
        write_queue: 处理完成帧队列
        output_size: 输出尺寸设置
        output_width, output_height: 输出分辨率
        worker_id: 工作线程ID
    """
    # 预分配内存缓冲区，减少内存分配开销
    # resize_buffer = None
    # if output_size != 'original':
    #     # 预分配缓冲区用于resize操作
    #     resize_buffer = np.empty((output_height, output_width, 3), dtype=np.uint8)

    try:
        processed_count = 0
        while True:
            item = process_queue.get()
            if item is None:  # 结束信号
                break

            frame, boxes, frame_count = item

            # CPU并行处理：批量绘制检测框（优化循环）
            if boxes is not None and len(boxes) > 0:
                # 使用numpy向量化操作加速
                boxes_int = boxes.astype(np.int32)
                # 批量绘制，减少函数调用开销
                color = (0, 255, 0)  # 预定义颜色
                thickness = 2
                for box in boxes_int:
                    cv2.rectangle(frame, (box[0], box[1]), (box[2], box[3]), color, thickness)

            # CPU并行处理：分辨率缩放（使用更快的插值方法）
            if output_size != 'original':
                # 使用INTER_AREA对于缩小更快，INTER_LINEAR对于放大
                interpolation = cv2.INTER_AREA if (output_width < frame.shape[1]) else cv2.INTER_LINEAR
                frame = cv2.resize(frame, (output_width, output_height), interpolation=interpolation)

            # 将处理完的帧放入写入队列
            write_queue.put((frame, frame_count))

            processed_count += 1
            # 大幅减少输出频率，避免日志影响性能
            if processed_count % 5000 == 0:
                print(f"🔧 线程{worker_id}: 已处理{processed_count}帧")

            process_queue.task_done()

    except Exception as e:
        print(f"❌ 帧处理线程{worker_id}错误: {e}")
    finally:
        process_queue.task_done()

def video_writer_worker(write_queue, out_writer, progress_info):
    """
    高分辨率优化的视频写入线程 - 立即写入，避免积压

    Args:
        write_queue: 待写入帧队列
        out_writer: 视频写入器
        progress_info: 进度信息共享字典
    """
    frame_buffer = {}  # 帧缓冲区，用于排序
    expected_frame = 1  # 期望的下一帧编号

    # 写入速度统计
    last_report_time = time.time()
    last_report_frames = 0

    try:
        frames_processed = 0
        while True:
            item = write_queue.get()
            if item is None:  # 结束信号
                break

            frame, frame_count = item

            # 将帧放入缓冲区
            frame_buffer[frame_count] = frame

            # 立即处理连续的帧，避免积压
            while expected_frame in frame_buffer:
                # 立即写入，不批量
                out_writer.write(frame_buffer[expected_frame])
                del frame_buffer[expected_frame]
                frames_processed += 1
                expected_frame += 1
                progress_info['processed_frames'] = frames_processed

                # 计算写入速度并输出
                if frames_processed % 1000 == 0:
                    current_time = time.time()
                    time_elapsed = current_time - last_report_time
                    frames_written = frames_processed - last_report_frames
                    write_speed = frames_written / time_elapsed if time_elapsed > 0 else 0

                    print(f"🎬 视频写入: 已处理{frames_processed}帧, 缓冲区:{len(frame_buffer)}帧, 写入速度:{write_speed:.1f}帧/秒")

                    # 更新统计基准
                    last_report_time = current_time
                    last_report_frames = frames_processed

            write_queue.task_done()

    except Exception as e:
        print(f"❌ 视频写入线程错误: {e}")
    finally:
        # 写入剩余的缓冲帧
        for frame_num in sorted(frame_buffer.keys()):
            out_writer.write(frame_buffer[frame_num])
        write_queue.task_done()

def detect_single_gpu(model_path, source, **kwargs):
    """
    单GPU检测

    Args:
        model_path: 模型文件路径
        source: 输入源(图片/视频/目录/URL)
        **kwargs: 其他参数
    """
    print(f"🚀 YOLO11 目标检测 (单GPU)")
    print(f"📦 模型: {model_path}")
    print(f"📁 输入: {source}")

    try:
        # 加载模型
        model = YOLO(model_path)
        print("✅ 模型加载成功")

        # 检测输入源类型
        source_type = _detect_source_type(source)
        print(f"🔍 检测到输入类型: {source_type}")

        # 根据输入类型优化参数
        if source_type == 'video' and not kwargs.get('save_frames', False):
            # 视频处理优化：使用流式处理，保存为mp4文件
            print("⚡ 启用视频流式处理优化...")

            # 抑制OpenCV的H.264解码警告
            suppress_opencv_warnings()

            # 获取视频信息用于计算总时长和设置输出
            cap = cv2.VideoCapture(source)
            # 设置视频解码参数，减少H.264解码错误
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 减少缓冲区大小
            fps = cap.get(cv2.CAP_PROP_FPS) or 25
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            video_duration = total_frames / fps  # 视频总时长（秒）
            cap.release()

            # 计算输出分辨率
            output_size = kwargs.get('output_size', 'original')
            output_width, output_height = get_output_resolution(original_width, original_height, output_size)

            # 计算缩放比例（用于后续可能的坐标转换）
            # scale_x = output_width / original_width
            # scale_y = output_height / original_height

            # 创建输出目录和文件路径
            output_dir = Path(kwargs.get('project', 'runs/detect')) / kwargs.get('name', 'exp')
            output_dir.mkdir(parents=True, exist_ok=True)
            size_suffix = f"_{output_size}" if output_size != 'original' else ""
            output_path = output_dir / f"{Path(source).stem}_detected{size_suffix}.mp4"

            # 初始化视频写入器（使用稳定高效的编码器）
            fourcc_options = [
                ('XVID', cv2.VideoWriter_fourcc(*'XVID')),  # 平衡速度和质量
                ('mp4v', cv2.VideoWriter_fourcc(*'mp4v')),  # 兼容性好
                ('H264', cv2.VideoWriter_fourcc(*'H264')),  # 质量好
            ]

            out_writer = None
            used_codec = None
            for codec_name, fourcc in fourcc_options:
                out_writer = cv2.VideoWriter(str(output_path), fourcc, fps, (output_width, output_height))
                if out_writer.isOpened():
                    used_codec = codec_name
                    break
                out_writer.release()

            if out_writer is None or not out_writer.isOpened():
                print(f"❌ 无法创建输出视频文件: {output_path}")
                return False

            print(f"🎬 使用编码器: {used_codec}")

            print(f"📹 输出视频: {output_path}")
            print(f"📊 原始分辨率: {original_width}x{original_height}")
            print(f"📊 输出分辨率: {output_width}x{output_height} ({output_size})")
            print(f"📊 视频信息: {total_frames}帧, {fps:.1f}fps, 时长{video_duration:.1f}秒")

            # 执行流式预测
            results = model.predict(
                source=source,
                stream=True,  # 关键：启用流式处理避免内存累积
                save=False,   # 不保存每帧图片
                save_txt=False,  # 不保存txt文件
                save_conf=False, # 不保存置信度文件
                conf=kwargs.get('conf', 0.25),
                iou=kwargs.get('iou', 0.45),
                imgsz=kwargs.get('imgsz', 640),
                device=kwargs.get('device', 'auto'),
                show=kwargs.get('show', False),
                verbose=False  # 关键：禁用详细输出，避免每帧都打印
            )

            # 多线程配置 - 回到稳定高效的配置
            cpu_count = os.cpu_count() or 16
            # 使用保守的线程数，避免过度竞争
            num_workers = min(20, cpu_count + 4)  # 16核+4个额外线程
            # 小而高效的队列大小
            queue_size = 120

            # 创建多级队列系统
            process_queue = queue.Queue(maxsize=queue_size)  # GPU -> CPU处理队列
            write_queue = queue.Queue(maxsize=queue_size)    # CPU处理 -> 写入队列
            progress_info = {'processed_frames': 0}

            print(f"🚀 启动高效CPU处理: {num_workers}个CPU处理线程, 1个快速写入线程")
            print(f"📦 缓冲区配置: 高效队列{queue_size}, 避免积压")
            print(f"💾 预计内存使用: {queue_size * 2 * original_width * original_height * 3 / 1024 / 1024 / 1024:.1f}GB")
            print(f"⚡ 优化策略: 保守线程数({num_workers}), 小缓冲区, 快速响应")
            print(f"🎯 目标: 稳定高效，避免资源竞争")

            # 启动多个帧处理工作线程（CPU密集型）
            processor_threads = []
            for i in range(num_workers):
                thread = Thread(
                    target=frame_processor_worker,
                    args=(process_queue, write_queue, output_size, output_width, output_height, i)
                )
                thread.daemon = True
                thread.start()
                processor_threads.append(thread)

            # 启动单个视频写入线程（VideoWriter不是线程安全的）
            writer_thread = Thread(
                target=video_writer_worker,
                args=(write_queue, out_writer, progress_info)
            )
            writer_thread.daemon = True
            writer_thread.start()

            # 处理流式结果
            frame_count = 0
            total_detections = 0
            start_time = time.time()

            # 推理时间统计
            inference_times = []
            total_inference_time = 0.0

            # 设置进度检查点（25%, 50%, 75%, 100%）
            progress_checkpoints = [0.25, 0.5, 0.75, 1.0]
            checkpoint_frames = [int(total_frames * cp) for cp in progress_checkpoints]
            checkpoint_index = 0

            try:
                # GPU推理主循环（专注于推理，不处理图像操作）
                for result in results:
                    frame_count += 1

                    # 记录推理开始时间
                    inference_start = time.perf_counter()

                    # 获取原始帧和检测结果
                    frame = result.orig_img.copy()  # 复制一份给CPU线程处理
                    boxes = None
                    if result.boxes is not None:
                        total_detections += len(result.boxes)
                        boxes = result.boxes.xyxy.cpu().numpy()

                    # 记录推理结束时间
                    inference_end = time.perf_counter()
                    frame_inference_time = (inference_end - inference_start) * 1000  # 转换为毫秒
                    inference_times.append(frame_inference_time)
                    total_inference_time += frame_inference_time

                    # 简化的队列控制
                    total_pending = process_queue.qsize() + write_queue.qsize()

                    # 简单有效的流控
                    if total_pending > queue_size * 0.8:  # 队列积压过多
                        time.sleep(0.001)  # 短暂等待

                    # 将帧数据放入处理队列，让CPU线程池并行处理
                    process_queue.put((frame, boxes, frame_count))

                    # 简化的进度检查（基于GPU推理进度）
                    if checkpoint_index < 4 and frame_count >= checkpoint_frames[checkpoint_index]:
                        progress = progress_checkpoints[checkpoint_index] * 100
                        elapsed_time = time.time() - start_time
                        processed = progress_info['processed_frames']
                        avg_inference = total_inference_time / frame_count if frame_count > 0 else 0
                        print(f"📊 进度: {progress:.0f}% (推理:{frame_count}/{total_frames}, 编码:{processed}/{total_frames}, 队列:{total_pending}), 已用时: {elapsed_time:.1f}秒, 平均推理: {avg_inference:.2f}ms")
                        checkpoint_index += 1

                # 发送结束信号给所有处理线程
                for _ in range(num_workers):
                    process_queue.put(None)

                # 等待所有处理线程完成
                for thread in processor_threads:
                    thread.join(timeout=60)

                # 发送结束信号给写入线程
                write_queue.put(None)

                # 等待写入线程完成
                writer_thread.join(timeout=60)

            finally:
                # 确保释放视频写入器
                out_writer.release()

            # 计算总耗时
            total_elapsed_time = time.time() - start_time

            # 计算推理统计
            avg_inference_time = total_inference_time / frame_count if frame_count > 0 else 0
            min_inference_time = min(inference_times) if inference_times else 0
            max_inference_time = max(inference_times) if inference_times else 0

            # 获取系统资源使用情况
            cpu_usage = "N/A"
            memory_usage = "N/A"
            if PSUTIL_AVAILABLE:
                try:
                    # 获取当前进程的CPU使用率（需要间隔时间计算）
                    process = psutil.Process()
                    # 先调用一次获取基准
                    process.cpu_percent()
                    time.sleep(0.1)  # 短暂等待
                    cpu_usage = f"{process.cpu_percent()}%"

                    # 获取当前进程的内存使用
                    memory_info = process.memory_info()
                    system_memory = psutil.virtual_memory()
                    memory_usage = f"{memory_info.rss / 1024 / 1024 / 1024:.1f}GB / {system_memory.total / 1024 / 1024 / 1024:.1f}GB"
                except Exception as e:
                    cpu_usage = f"Error: {e}"

            # 计算写入性能统计
            final_write_speed = progress_info['processed_frames'] / total_elapsed_time if total_elapsed_time > 0 else 0

            # 详细的最终统计
            print(f"✅ 视频处理完成!")
            print(f"   总帧数: {frame_count}")
            print(f"   总检测数: {total_detections}")
            print(f"   视频总时长: {video_duration:.1f}秒")
            print(f"   处理总耗时: {total_elapsed_time:.1f}秒")
            print(f"   处理速度: {frame_count/total_elapsed_time:.1f}帧/秒")
            print(f"   平均推理用时: {avg_inference_time:.2f}ms")
            print(f"   推理用时范围: {min_inference_time:.2f}ms - {max_inference_time:.2f}ms")
            print(f"   总推理时间: {total_inference_time/1000:.2f}秒 ({total_inference_time/total_elapsed_time/10:.1f}%)")
            print(f"   平均写入速度: {final_write_speed:.1f}帧/秒")
            print(f"   使用编码器: {used_codec}")
            print(f"   CPU线程数: {num_workers}")
            print(f"   队列大小: {queue_size}")
            print(f"   CPU使用率: {cpu_usage}")
            print(f"   内存使用: {memory_usage}")
            print(f"🎬 输出视频已保存: {output_path}")

        else:
            # 图片或需要保存帧的视频处理
            print("📸 使用标准处理模式...")

            # 执行标准预测
            results = model.predict(
                source=source,
                save=kwargs.get('save', True),
                save_txt=kwargs.get('save_txt', True),
                save_conf=kwargs.get('save_conf', True),
                conf=kwargs.get('conf', 0.25),
                iou=kwargs.get('iou', 0.45),
                imgsz=kwargs.get('imgsz', 640),
                device=kwargs.get('device', 'auto'),
                project=kwargs.get('project', 'runs/detect'),
                name=kwargs.get('name', 'exp'),
                exist_ok=kwargs.get('exist_ok', True),
                show=kwargs.get('show', False),
                verbose=kwargs.get('verbose', True)
            )

            print(f"✅ 检测完成! 结果保存在: runs/detect/{kwargs.get('name', 'exp')}/")

        return True

    except Exception as e:
        print(f"❌ 检测失败: {e}")
        return False

def detect_worker(gpu_id, model_path, source_batch, batch_id, **kwargs):
    """
    多GPU工作线程

    Args:
        gpu_id: GPU设备ID
        model_path: 模型路径
        source_batch: 输入源批次
        batch_id: 批次ID
        **kwargs: 其他参数
    """
    try:
        # 为每个线程创建独立的模型实例(线程安全)
        model = YOLO(model_path)

        # 设置GPU设备
        device = f"cuda:{gpu_id}" if torch.cuda.is_available() else "cpu"

        print(f"🔄 GPU-{gpu_id} 处理批次-{batch_id}: {len(source_batch)}个文件")

        # 执行预测
        _ = model.predict(
            source=source_batch,
            device=device,
            save=kwargs.get('save', True),
            save_txt=kwargs.get('save_txt', True),
            save_conf=kwargs.get('save_conf', True),
            conf=kwargs.get('conf', 0.25),
            iou=kwargs.get('iou', 0.45),
            imgsz=kwargs.get('imgsz', 640),
            project=kwargs.get('project', 'runs/detect'),
            name=f"{kwargs.get('name', 'exp')}_gpu{gpu_id}_batch{batch_id}",
            exist_ok=True,
            verbose=False
        )

        print(f"✅ GPU-{gpu_id} 批次-{batch_id} 完成")
        return True

    except Exception as e:
        print(f"❌ GPU-{gpu_id} 批次-{batch_id} 失败: {e}")
        return False

def detect_multi_gpu(model_path, source, num_gpus=None, **kwargs):
    """
    多GPU并行检测

    Args:
        model_path: 模型文件路径
        source: 输入源目录
        num_gpus: 使用的GPU数量
        **kwargs: 其他参数
    """
    # 检查GPU可用性
    if not torch.cuda.is_available():
        print("❌ CUDA不可用，回退到单GPU模式")
        return detect_single_gpu(model_path, source, **kwargs)

    available_gpus = torch.cuda.device_count()
    if num_gpus is None:
        num_gpus = available_gpus
    else:
        num_gpus = min(num_gpus, available_gpus)

    if num_gpus <= 1:
        print("🔄 只有1个GPU可用，使用单GPU模式")
        return detect_single_gpu(model_path, source, **kwargs)

    print(f"🚀 YOLO11 多GPU并行检测")
    print(f"📦 模型: {model_path}")
    print(f"📁 输入: {source}")
    print(f"🎯 使用GPU数量: {num_gpus}/{available_gpus}")

    # 获取输入文件列表
    source_path = Path(source)
    if source_path.is_file():
        source_files = [str(source_path)]
    elif source_path.is_dir():
        # 支持常见图片和视频格式
        extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.mp4', '*.avi', '*.mov', '*.mkv']
        source_files = []
        for ext in extensions:
            source_files.extend(source_path.glob(ext))
            source_files.extend(source_path.glob(ext.upper()))
        source_files = [str(f) for f in source_files]
    else:
        print(f"❌ 输入源无效: {source}")
        return False

    if not source_files:
        print(f"❌ 未找到有效的输入文件")
        return False

    print(f"📊 总文件数: {len(source_files)}")

    # 将文件分配到不同GPU
    batch_size = max(1, len(source_files) // num_gpus)
    batches = []
    for i in range(0, len(source_files), batch_size):
        batch = source_files[i:i + batch_size]
        if batch:  # 确保批次不为空
            batches.append(batch)

    # 限制批次数量不超过GPU数量
    if len(batches) > num_gpus:
        # 重新分配，确保每个GPU都有任务
        new_batches = [[] for _ in range(num_gpus)]
        for i, file in enumerate(source_files):
            new_batches[i % num_gpus].append(file)
        batches = [batch for batch in new_batches if batch]

    print(f"📦 分批情况: {len(batches)}个批次")
    for i, batch in enumerate(batches):
        print(f"  批次-{i}: {len(batch)}个文件 -> GPU-{i % num_gpus}")

    # 使用线程池执行多GPU推理
    success_count = 0
    with ThreadPoolExecutor(max_workers=num_gpus) as executor:
        futures = []
        for i, batch in enumerate(batches):
            gpu_id = i % num_gpus
            future = executor.submit(
                detect_worker,
                gpu_id,
                model_path,
                batch,
                i,
                **kwargs
            )
            futures.append(future)

        # 等待所有任务完成
        for future in as_completed(futures):
            if future.result():
                success_count += 1

    print(f"✅ 多GPU检测完成! 成功: {success_count}/{len(batches)}")
    return success_count == len(batches)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="YOLO11 目标检测 - 支持TensorRT加速和多GPU并行",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 基础检测
  python detect.py --source image.jpg                    # 检测单张图片
  python detect.py --source video.mp4                    # 检测视频(流式处理,快速)
  python detect.py --source video.mp4 --save-frames      # 检测视频并保存每帧
  python detect.py --source images/                      # 检测目录中所有图片
  python detect.py --source 0                            # 使用摄像头

  # TensorRT加速
  python detect.py --source images/ --tensorrt           # 自动导出并使用TensorRT
  python detect.py --source video.mp4 --tensorrt         # TensorRT加速视频处理
  python detect.py --source images/ --tensorrt --half    # 使用FP16精度
  python detect.py --source images/ --tensorrt --int8    # 使用INT8量化

  # 多GPU并行
  python detect.py --source images/ --multi-gpu          # 使用所有可用GPU
  python detect.py --source images/ --multi-gpu --num-gpus 2  # 使用2个GPU

  # 组合使用
  python detect.py --source video.mp4 --tensorrt --conf 0.5   # TensorRT加速视频检测
        """
    )

    # 必需参数
    parser.add_argument('--source', '-s', required=True,
                       help='输入源: 图片/视频/目录/摄像头ID/URL')

    # 模型参数
    parser.add_argument('--model', '-m', default=None,
                       help='模型路径 (默认: 自动查找)')

    # 检测参数
    parser.add_argument('--conf', type=float, default=0.25,
                       help='置信度阈值 (默认: 0.25)')
    parser.add_argument('--iou', type=float, default=0.45,
                       help='NMS IoU阈值 (默认: 0.45)')
    parser.add_argument('--imgsz', type=int, default=640,
                       help='输入图像尺寸 (默认: 640)')
    parser.add_argument('--device', default='auto',
                       help='设备: auto/cpu/0/1/... (默认: auto)')

    # 输出参数
    parser.add_argument('--project', default='runs/detect',
                       help='输出项目目录 (默认: runs/detect)')
    parser.add_argument('--name', default='exp',
                       help='实验名称 (默认: exp)')
    parser.add_argument('--save-txt', action='store_true',
                       help='保存检测结果为txt文件')
    parser.add_argument('--save-conf', action='store_true',
                       help='保存置信度到txt文件')
    parser.add_argument('--save-frames', action='store_true',
                       help='保存视频的每一帧图片 (默认: 仅对图片保存)')
    parser.add_argument('--show', action='store_true',
                       help='显示检测结果')
    parser.add_argument('--verbose', action='store_true', default=True,
                       help='详细输出')

    # TensorRT加速参数
    parser.add_argument('--tensorrt', action='store_true',
                       help='使用TensorRT加速推理')
    parser.add_argument('--half', action='store_true',
                       help='使用FP16精度 (TensorRT)')
    parser.add_argument('--int8', action='store_true',
                       help='使用INT8量化 (TensorRT)')
    parser.add_argument('--workspace', type=int, default=4,
                       help='TensorRT工作空间大小(GB) (默认: 4)')
    parser.add_argument('--data', default='coco.yaml',
                       help='数据集配置文件 (INT8量化需要)')

    # 多GPU参数
    parser.add_argument('--multi-gpu', action='store_true',
                       help='使用多GPU并行推理')
    parser.add_argument('--num-gpus', type=int, default=None,
                       help='使用的GPU数量 (默认: 所有可用GPU)')

    # 性能优化参数
    parser.add_argument('--output-size', default='original',
                       choices=['original', '1080p', '720p', '480p'],
                       help='输出视频分辨率 (默认: original) - 降低分辨率可大幅提升处理速度')

    args = parser.parse_args()

    # 确定模型路径
    model_path = args.model or find_model()
    if not model_path:
        print("❌ 错误: 未找到可用模型")
        print("💡 请:")
        print("   1. 指定模型路径: --model path/to/model.pt")
        print("   2. 或下载预训练模型: yolo11n.pt")
        sys.exit(1)

    if not Path(model_path).exists():
        print(f"❌ 错误: 模型文件不存在: {model_path}")
        sys.exit(1)

    # 检查输入源(除了摄像头ID和URL)
    if not (args.source.isdigit() or args.source.startswith(('http://', 'https://'))):
        if not Path(args.source).exists():
            print(f"❌ 错误: 输入源不存在: {args.source}")
            sys.exit(1)

    # TensorRT加速处理
    if args.tensorrt:
        print("🔧 启用TensorRT加速...")

        # 检查是否已有TensorRT模型
        engine_path = str(Path(model_path).with_suffix('.engine'))
        if not Path(engine_path).exists():
            print("🔄 未找到TensorRT模型，开始导出...")
            try:
                engine_path = export_tensorrt(
                    model_path,
                    imgsz=args.imgsz,
                    half=args.half,
                    int8=args.int8,
                    workspace=args.workspace,
                    data=args.data,
                    device=args.device if args.device != 'auto' else 0
                )
                if engine_path and Path(engine_path).exists():
                    model_path = engine_path
                else:
                    print("❌ TensorRT导出失败，使用原始模型")
            except Exception as e:
                print(f"❌ TensorRT导出失败: {e}")
                print("⚠️ 使用原始模型继续...")
        else:
            print(f"✅ 找到现有TensorRT模型: {engine_path}")
            # 验证TensorRT模型是否可用
            try:
                from ultralytics import YOLO
                _ = YOLO(engine_path)  # 验证模型可以加载
                model_path = engine_path
                print("✅ TensorRT模型验证成功")
            except Exception as e:
                print(f"⚠️ TensorRT模型验证失败: {e}")
                print("⚠️ 使用原始模型继续...")

    # 执行检测
    if args.multi_gpu and Path(args.source).is_dir():
        # 多GPU模式(仅支持目录输入)
        success = detect_multi_gpu(
            model_path=model_path,
            source=args.source,
            num_gpus=args.num_gpus,
            conf=args.conf,
            iou=args.iou,
            imgsz=args.imgsz,
            project=args.project,
            name=args.name,
            save_txt=args.save_txt,
            save_conf=args.save_conf,
            show=args.show,
            verbose=args.verbose
        )
    else:
        # 单GPU模式
        if args.multi_gpu:
            print("⚠️  多GPU模式仅支持目录输入，回退到单GPU模式")

        success = detect_single_gpu(
            model_path=model_path,
            source=args.source,
            conf=args.conf,
            iou=args.iou,
            imgsz=args.imgsz,
            device=args.device,
            project=args.project,
            name=args.name,
            save_txt=args.save_txt,
            save_conf=args.save_conf,
            save_frames=args.save_frames,
            show=args.show,
            verbose=args.verbose,
            output_size=args.output_size
        )

    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
