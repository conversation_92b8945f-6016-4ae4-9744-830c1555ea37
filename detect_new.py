#!/usr/bin/env python3
"""
YOLO11 目标检测推理脚本 - 终极优化版本
结合多线程架构 + GPU加速 + 批处理优化
"""

from ultralytics import YOLO
from pathlib import Path
import argparse
import sys
import time
import cv2
import torch
import queue
from threading import Thread
import os
import numpy as np

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

def suppress_opencv_warnings():
    """抑制OpenCV的H.264解码警告"""
    try:
        cv2.setLogLevel(cv2.LOG_LEVEL_ERROR)
    except AttributeError:
        try:
            cv2.setLogLevel(3)
        except:
            pass

def _detect_source_type(source):
    """检测输入源类型"""
    if str(source).isdigit():
        return 'camera'
    if str(source).startswith(('http://', 'https://', 'rtmp://', 'rtsp://')):
        return 'url'
    
    source_path = Path(source)
    if source_path.exists():
        if source_path.is_dir():
            return 'directory'
        elif source_path.is_file():
            ext = source_path.suffix.lower()
            video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.m4v', '.3gp', '.webm'}
            if ext in video_extensions:
                return 'video'
            else:
                return 'image'
    return 'image'

def get_output_resolution(original_width, original_height, target_size):
    """根据目标尺寸计算输出分辨率"""
    if target_size == 'original':
        return original_width, original_height
    elif target_size == '1080p':
        aspect_ratio = original_width / original_height
        if aspect_ratio > 16/9:
            return 1920, int(1920 / aspect_ratio)
        else:
            return int(1080 * aspect_ratio), 1080
    elif target_size == '720p':
        aspect_ratio = original_width / original_height
        if aspect_ratio > 16/9:
            return 1280, int(1280 / aspect_ratio)
        else:
            return int(720 * aspect_ratio), 720
    elif target_size == '480p':
        aspect_ratio = original_width / original_height
        if aspect_ratio > 16/9:
            return 854, int(854 / aspect_ratio)
        else:
            return int(480 * aspect_ratio), 480
    else:
        return original_width, original_height

def find_model():
    """查找可用模型"""
    model_paths = [
        "runs/train/*/weights/best.pt",
        "runs/train/*/weights/last.pt",
        "yolo11n.pt", "yolo11s.pt", "yolo11m.pt", "yolo11l.pt", "yolo11x.pt"
    ]
    
    for pattern in model_paths:
        if '*' in pattern:
            matches = list(Path('.').glob(pattern))
            if matches:
                return str(matches[0])
        else:
            if Path(pattern).exists():
                return pattern
    return None

def export_tensorrt(model_path, **kwargs):
    """导出TensorRT模型"""
    print(f"🔧 导出TensorRT模型...")
    print(f"📦 原始模型: {model_path}")
    
    try:
        model = YOLO(model_path)
        export_args = {
            'format': 'engine',
            'imgsz': kwargs.get('imgsz', 640),
            'half': kwargs.get('half', True),
            'int8': kwargs.get('int8', False),
            'dynamic': kwargs.get('dynamic', False),
            'workspace': kwargs.get('workspace', 16),
            'device': kwargs.get('device', 0),
            'simplify': kwargs.get('simplify', True),
            'verbose': False
        }
        
        if export_args['int8']:
            export_args['data'] = kwargs.get('data', 'coco.yaml')
        
        print("🔧 TensorRT导出配置:")
        for key, value in export_args.items():
            print(f"  {key}: {value}")
        
        model.export(**export_args)
        engine_path = str(Path(model_path).with_suffix('.engine'))
        print(f"✅ TensorRT模型导出成功: {engine_path}")
        return engine_path
    
    except Exception as e:
        print(f"❌ TensorRT导出失败: {e}")
        return None

class UltimateVideoProcessor:
    """终极视频处理器 - 多线程 + GPU加速 + 批处理"""
    
    def __init__(self, model_path, output_size='original'):
        self.model = YOLO(model_path)
        self.output_size = output_size
        
        # 检测GPU加速能力
        self.gpu_available = False
        try:
            if cv2.cuda.getCudaEnabledDeviceCount() > 0:
                self.gpu_available = True
                print("🚀 检测到CUDA GPU，启用GPU加速处理")
            else:
                print("⚠️ 未检测到CUDA GPU，使用CPU处理")
        except (cv2.error, AttributeError):
            print("⚠️ OpenCV未编译CUDA支持，使用CPU处理")
    
    def ultra_fast_frame_processor(self, process_queue, write_queue, output_width, output_height, worker_id):
        """超快帧处理工作线程"""
        processed_count = 0
        
        try:
            while True:
                item = process_queue.get()
                if item is None:
                    break
                
                frame, boxes, frame_count = item
                
                # 使用优化的帧处理
                processed_frame = self._process_frame_ultra_fast(
                    frame, boxes, output_width, output_height
                )
                
                write_queue.put((processed_frame, frame_count))
                processed_count += 1
                
                if processed_count % 5000 == 0:
                    print(f"🔧 线程{worker_id}: 已处理{processed_count}帧")
                
                process_queue.task_done()
        
        except Exception as e:
            print(f"❌ 帧处理线程{worker_id}错误: {e}")
        finally:
            process_queue.task_done()
    
    def _process_frame_ultra_fast(self, frame, boxes, output_width, output_height):
        """超快帧处理 - GPU加速版本"""
        original_height, original_width = frame.shape[:2]
        
        # 先resize再绘制（减少绘制工作量）
        if self.output_size != 'original':
            scale_x = output_width / original_width
            scale_y = output_height / original_height
            
            # GPU加速resize
            if self.gpu_available:
                try:
                    gpu_frame = cv2.cuda_GpuMat()
                    gpu_frame.upload(frame)
                    gpu_resized = cv2.cuda.resize(gpu_frame, (output_width, output_height), interpolation=cv2.INTER_NEAREST)
                    frame = gpu_resized.download()
                except:
                    frame = cv2.resize(frame, (output_width, output_height), interpolation=cv2.INTER_NEAREST)
            else:
                frame = cv2.resize(frame, (output_width, output_height), interpolation=cv2.INTER_NEAREST)
            
            # 调整检测框坐标
            if boxes is not None and len(boxes) > 0:
                boxes = boxes * [scale_x, scale_y, scale_x, scale_y]
        
        # 极速绘制检测框
        if boxes is not None and len(boxes) > 0:
            boxes_int = boxes.astype(np.int32)
            
            # 智能过滤大框，减少绘制工作量
            if len(boxes_int) > 30:
                areas = (boxes_int[:, 2] - boxes_int[:, 0]) * (boxes_int[:, 3] - boxes_int[:, 1])
                large_boxes = boxes_int[areas > 800]
                for box in large_boxes:
                    cv2.rectangle(frame, (box[0], box[1]), (box[2], box[3]), (0, 255, 0), 1)
            else:
                for box in boxes_int:
                    cv2.rectangle(frame, (box[0], box[1]), (box[2], box[3]), (0, 255, 0), 1)
        
        return frame
    
    def ultra_fast_video_writer(self, write_queue, out_writer, progress_info):
        """超快视频写入线程"""
        frame_buffer = {}
        expected_frame = 1
        last_report_time = time.time()
        last_report_frames = 0
        
        try:
            frames_processed = 0
            while True:
                item = write_queue.get()
                if item is None:
                    break
                
                frame, frame_count = item
                frame_buffer[frame_count] = frame
                
                # 立即写入连续帧
                while expected_frame in frame_buffer:
                    out_writer.write(frame_buffer[expected_frame])
                    del frame_buffer[expected_frame]
                    frames_processed += 1
                    expected_frame += 1
                    progress_info['processed_frames'] = frames_processed
                
                # 写入速度统计
                if frames_processed % 1000 == 0:
                    current_time = time.time()
                    time_elapsed = current_time - last_report_time
                    frames_written = frames_processed - last_report_frames
                    write_speed = frames_written / time_elapsed if time_elapsed > 0 else 0
                    
                    print(f"🎬 视频写入: 已处理{frames_processed}帧, 缓冲区:{len(frame_buffer)}帧, 写入速度:{write_speed:.1f}帧/秒")
                    
                    last_report_time = current_time
                    last_report_frames = frames_processed
                
                write_queue.task_done()
        
        except Exception as e:
            print(f"❌ 视频写入线程错误: {e}")
        finally:
            # 写入剩余帧
            for frame_num in sorted(frame_buffer.keys()):
                out_writer.write(frame_buffer[frame_num])
            write_queue.task_done()

    def process_video_ultimate(self, source, **kwargs):
        """终极视频处理方法"""
        print("🚀 启动终极视频处理模式...")

        # 抑制OpenCV警告
        suppress_opencv_warnings()

        # 获取视频信息
        cap = cv2.VideoCapture(source)
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        fps = cap.get(cv2.CAP_PROP_FPS) or 25
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        video_duration = total_frames / fps
        cap.release()

        # 计算输出分辨率
        output_width, output_height = get_output_resolution(original_width, original_height, self.output_size)

        # 创建输出路径
        output_dir = Path(kwargs.get('project', 'runs/detect')) / kwargs.get('name', 'exp')
        output_dir.mkdir(parents=True, exist_ok=True)
        size_suffix = f"_{self.output_size}" if self.output_size != 'original' else ""
        output_path = output_dir / f"{Path(source).stem}_detected{size_suffix}.mp4"

        print(f"📹 输出视频: {output_path}")
        print(f"📊 原始分辨率: {original_width}x{original_height}")
        print(f"📊 输出分辨率: {output_width}x{output_height} ({self.output_size})")
        print(f"📊 视频信息: {total_frames}帧, {fps:.1f}fps, 时长{video_duration:.1f}秒")

        # 使用最快的编码器
        fourcc_options = [
            ('mp4v', cv2.VideoWriter_fourcc(*'mp4v')),
            ('XVID', cv2.VideoWriter_fourcc(*'XVID')),
        ]

        out_writer = None
        used_codec = None
        for codec_name, fourcc in fourcc_options:
            out_writer = cv2.VideoWriter(str(output_path), fourcc, fps, (output_width, output_height))
            if out_writer.isOpened():
                used_codec = codec_name
                break
            out_writer.release()

        if out_writer is None or not out_writer.isOpened():
            print(f"❌ 无法创建输出视频文件: {output_path}")
            return False

        print(f"🎬 使用编码器: {used_codec}")

        # 终极多线程配置
        cpu_count = os.cpu_count() or 16
        num_workers = min(cpu_count, 16)  # 最多16个处理线程
        queue_size = 100  # 适中的队列大小

        # 创建队列系统
        process_queue = queue.Queue(maxsize=queue_size)
        write_queue = queue.Queue(maxsize=queue_size)
        progress_info = {'processed_frames': 0}

        print(f"🚀 启动终极多线程处理: {num_workers}个CPU处理线程, 1个写入线程")
        print(f"📦 缓冲区配置: 队列{queue_size}, GPU加速: {'是' if self.gpu_available else '否'}")

        # 启动处理线程
        processor_threads = []
        for i in range(num_workers):
            thread = Thread(
                target=self.ultra_fast_frame_processor,
                args=(process_queue, write_queue, output_width, output_height, i)
            )
            thread.daemon = True
            thread.start()
            processor_threads.append(thread)

        # 启动写入线程
        writer_thread = Thread(
            target=self.ultra_fast_video_writer,
            args=(write_queue, out_writer, progress_info)
        )
        writer_thread.daemon = True
        writer_thread.start()

        # 执行YOLO推理
        results = self.model.predict(
            source=source,
            stream=True,
            save=False,
            save_txt=False,
            save_conf=False,
            conf=kwargs.get('conf', 0.25),
            iou=kwargs.get('iou', 0.45),
            imgsz=kwargs.get('imgsz', 640),
            device=kwargs.get('device', 'auto'),
            show=kwargs.get('show', False),
            verbose=False
        )

        # 主推理循环
        frame_count = 0
        total_detections = 0
        start_time = time.time()
        inference_times = []

        try:
            for result in results:
                inference_start = time.perf_counter()

                frame_count += 1
                frame = result.orig_img  # 不复制
                boxes = None

                if result.boxes is not None:
                    total_detections += len(result.boxes)
                    boxes = result.boxes.xyxy.cpu().numpy()

                inference_end = time.perf_counter()
                inference_times.append((inference_end - inference_start) * 1000)

                # 放入处理队列
                process_queue.put((frame, boxes, frame_count))

                # 流控
                if process_queue.qsize() > queue_size * 0.8:
                    time.sleep(0.001)

                # 进度报告
                if frame_count % 1000 == 0:
                    elapsed = time.time() - start_time
                    speed = frame_count / elapsed if elapsed > 0 else 0
                    avg_inference = sum(inference_times) / len(inference_times) if inference_times else 0
                    processed = progress_info['processed_frames']
                    print(f"📊 进度: {frame_count}/{total_frames} ({frame_count/total_frames*100:.1f}%), "
                          f"速度: {speed:.1f}帧/秒, 平均推理: {avg_inference:.2f}ms, 编码: {processed}")

            # 等待处理完成
            for _ in range(num_workers):
                process_queue.put(None)

            for thread in processor_threads:
                thread.join(timeout=60)

            write_queue.put(None)
            writer_thread.join(timeout=60)

        finally:
            out_writer.release()

        # 最终统计
        total_elapsed_time = time.time() - start_time
        avg_inference_time = sum(inference_times) / len(inference_times) if inference_times else 0
        total_inference_time = sum(inference_times)

        print(f"✅ 终极视频处理完成!")
        print(f"   总帧数: {frame_count}")
        print(f"   总检测数: {total_detections}")
        print(f"   视频总时长: {video_duration:.1f}秒")
        print(f"   处理总耗时: {total_elapsed_time:.1f}秒")
        print(f"   处理速度: {frame_count/total_elapsed_time:.1f}帧/秒")
        print(f"   平均推理用时: {avg_inference_time:.2f}ms")
        print(f"   总推理时间: {total_inference_time/1000:.2f}秒 ({total_inference_time/total_elapsed_time/10:.1f}%)")
        print(f"   使用编码器: {used_codec}")
        print(f"   CPU线程数: {num_workers}")
        print(f"   GPU加速: {'是' if self.gpu_available else '否'}")

        return True

def detect_single_gpu_ultimate(model_path, source, **kwargs):
    """终极单GPU检测"""
    print(f"🚀 YOLO11 终极目标检测")
    print(f"📦 模型: {model_path}")
    print(f"📁 输入: {source}")

    try:
        source_type = _detect_source_type(source)
        print(f"🔍 检测到输入类型: {source_type}")

        if source_type == 'video':
            processor = UltimateVideoProcessor(model_path, kwargs.get('output_size', 'original'))
            return processor.process_video_ultimate(source, **kwargs)
        else:
            # 对于非视频，使用标准处理
            model = YOLO(model_path)
            _ = model.predict(
                source=source,
                save=kwargs.get('save', True),
                save_txt=kwargs.get('save_txt', True),
                save_conf=kwargs.get('save_conf', True),
                conf=kwargs.get('conf', 0.25),
                iou=kwargs.get('iou', 0.45),
                imgsz=kwargs.get('imgsz', 640),
                device=kwargs.get('device', 'auto'),
                project=kwargs.get('project', 'runs/detect'),
                name=kwargs.get('name', 'exp'),
                exist_ok=kwargs.get('exist_ok', True),
                show=kwargs.get('show', False),
                verbose=kwargs.get('verbose', True)
            )
            print(f"✅ 检测完成! 结果保存在: runs/detect/{kwargs.get('name', 'exp')}/")
            return True

    except Exception as e:
        print(f"❌ 检测失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="YOLO11 终极目标检测 - 多线程+GPU加速版本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 终极视频检测
  python detect_final.py --source video.mp4 --tensorrt --output-size 1080p
  python detect_final.py --source video.mp4 --tensorrt --conf 0.5
        """
    )

    # 必需参数
    parser.add_argument('--source', '-s', required=True,
                       help='输入源: 图片/视频/目录/摄像头ID/URL')

    # 模型参数
    parser.add_argument('--model', '-m', default=None,
                       help='模型路径 (默认: 自动查找)')

    # 检测参数
    parser.add_argument('--conf', type=float, default=0.25,
                       help='置信度阈值 (默认: 0.25)')
    parser.add_argument('--iou', type=float, default=0.45,
                       help='NMS IoU阈值 (默认: 0.45)')
    parser.add_argument('--imgsz', type=int, default=640,
                       help='输入图像尺寸 (默认: 640)')
    parser.add_argument('--device', default='auto',
                       help='设备: auto/cpu/0/1/... (默认: auto)')

    # 输出参数
    parser.add_argument('--project', default='runs/detect',
                       help='输出项目目录 (默认: runs/detect)')
    parser.add_argument('--name', default='exp',
                       help='实验名称 (默认: exp)')
    parser.add_argument('--save-txt', action='store_true',
                       help='保存检测结果为txt文件')
    parser.add_argument('--save-conf', action='store_true',
                       help='保存置信度到txt文件')
    parser.add_argument('--show', action='store_true',
                       help='显示检测结果')
    parser.add_argument('--verbose', action='store_true', default=True,
                       help='详细输出')

    # TensorRT加速参数
    parser.add_argument('--tensorrt', action='store_true',
                       help='使用TensorRT加速推理')
    parser.add_argument('--half', action='store_true',
                       help='使用FP16精度 (TensorRT)')
    parser.add_argument('--int8', action='store_true',
                       help='使用INT8量化 (TensorRT)')
    parser.add_argument('--workspace', type=int, default=4,
                       help='TensorRT工作空间大小(GB) (默认: 4)')
    parser.add_argument('--data', default='coco.yaml',
                       help='数据集配置文件 (INT8量化需要)')

    # 性能优化参数
    parser.add_argument('--output-size', default='original',
                       choices=['original', '1080p', '720p', '480p'],
                       help='输出视频分辨率 (默认: original)')

    args = parser.parse_args()

    # 确定模型路径
    model_path = args.model or find_model()
    if not model_path:
        print("❌ 错误: 未找到可用模型")
        print("💡 请:")
        print("   1. 指定模型路径: --model path/to/model.pt")
        print("   2. 或下载预训练模型: yolo11n.pt")
        sys.exit(1)

    if not Path(model_path).exists():
        print(f"❌ 错误: 模型文件不存在: {model_path}")
        sys.exit(1)

    # 检查输入源
    if not (args.source.isdigit() or args.source.startswith(('http://', 'https://'))):
        if not Path(args.source).exists():
            print(f"❌ 错误: 输入源不存在: {args.source}")
            sys.exit(1)

    # TensorRT加速处理
    if args.tensorrt:
        print("🔧 启用TensorRT加速...")
        engine_path = str(Path(model_path).with_suffix('.engine'))
        if not Path(engine_path).exists():
            print("🔄 未找到TensorRT模型，开始导出...")
            try:
                engine_path = export_tensorrt(
                    model_path,
                    imgsz=args.imgsz,
                    half=args.half,
                    int8=args.int8,
                    workspace=args.workspace,
                    data=args.data,
                    device=args.device if args.device != 'auto' else 0
                )
                if engine_path and Path(engine_path).exists():
                    model_path = engine_path
                else:
                    print("❌ TensorRT导出失败，使用原始模型")
            except Exception as e:
                print(f"❌ TensorRT导出失败: {e}")
                print("⚠️ 使用原始模型继续...")
        else:
            print(f"✅ 找到现有TensorRT模型: {engine_path}")
            try:
                _ = YOLO(engine_path)
                model_path = engine_path
                print("✅ TensorRT模型验证成功")
            except Exception as e:
                print(f"⚠️ TensorRT模型验证失败: {e}")
                print("⚠️ 使用原始模型继续...")

    # 执行终极检测
    success = detect_single_gpu_ultimate(
        model_path=model_path,
        source=args.source,
        conf=args.conf,
        iou=args.iou,
        imgsz=args.imgsz,
        device=args.device,
        project=args.project,
        name=args.name,
        save_txt=args.save_txt,
        save_conf=args.save_conf,
        show=args.show,
        verbose=args.verbose,
        output_size=args.output_size
    )

    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
